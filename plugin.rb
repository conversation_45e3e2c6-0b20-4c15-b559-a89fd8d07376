# name: getverified-alert
# about: Shows an information alert above the navbar for unverified users
# version: 2.0.0
# authors: Discourse Plugin Developer
# url: https://github.com/yourusername/discourse-getverified-alert
# required_version: 3.0.0

enabled_site_setting :getverified_alert_enabled

register_asset "stylesheets/getverified-alert.scss"

after_initialize do
  begin
    Rails.logger.info "[GetVerified Alert] Plugin initialized successfully"

    # Add a safety check for required groups
    if defined?(Group)
      required_groups = ['unverified', 'grace_period', 'verified']
      missing_groups = required_groups.select { |name| !Group.exists?(name: name) }

      if missing_groups.any?
        Rails.logger.warn "[GetVerified Alert] Missing groups: #{missing_groups.join(', ')}. Plugin will work but may not show alerts until groups are created."
      else
        Rails.logger.info "[GetVerified Alert] All required groups found: #{required_groups.join(', ')}"
      end
    end

  rescue => e
    Rails.logger.error "[GetVerified Alert] Error during plugin initialization: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
  end
end
