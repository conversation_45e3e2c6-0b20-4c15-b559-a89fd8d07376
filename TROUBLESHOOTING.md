# GetVerified Alert Plugin - Troubleshooting Guide

## 🚨 Critical Issues Fixed

This document addresses the critical issues that were identified and resolved:

### ✅ Issue 1: Page Loading Problems
**Problem**: Main Discourse page not loading after plugin installation
**Root Cause**: JavaScript errors and infinite loops in group checking logic
**Solution**: Added comprehensive error handling and safeguards

### ✅ Issue 2: Alert Stacking
**Problem**: Multiple alert banners appearing stacked
**Root Cause**: Component setup running multiple times without cleanup
**Solution**: Added cleanup mechanisms and duplicate prevention

### ✅ Issue 3: Missing Group Handling
**Problem**: Plugin crashes when required groups don't exist
**Root Cause**: No null checks or error handling for missing groups
**Solution**: Added graceful fallbacks and error handling

## 🔧 Debugging Tools

### Console Commands
Open browser console and use these commands for debugging:

```javascript
// Check if plugin is loaded
console.log(window.GetVerifiedAlertSafety);

// Manual cleanup of all alerts
window.GetVerifiedAlertSafety.cleanup();

// Reset plugin state and reload page
window.GetVerifiedAlertSafety.reset();

// Check current user groups
console.log(Discourse.User.current()?.groups);

// Check plugin settings
console.log(Discourse.SiteSettings);
```

### Log Messages
Look for these log messages in browser console:

- `[GetVerified Alert] Setting up component` - Component initialization
- `[GetVerified Alert] Computing shouldShowAlert` - Alert visibility check
- `[GetVerified Alert] User groups: [...]` - User's group membership
- `[GetVerified Alert Safety] Multiple alerts detected` - Stacking prevention

## 🛠️ Installation Steps (Updated)

1. **Install Plugin**:
   ```bash
   cd /var/discourse/plugins
   git clone [repository-url] discourse-getverified-alert
   ```

2. **Rebuild Container**:
   ```bash
   cd /var/discourse
   ./launcher rebuild app
   ```

3. **Enable Plugin** (IMPORTANT):
   - Go to Admin → Settings → Plugins
   - Find `getverified_alert_enabled` and set to `true`
   - Plugin is disabled by default for safety

4. **Create Required Groups**:
   - Go to Admin → Groups
   - Create groups: `unverified`, `grace_period`, `verified`
   - Add test users to these groups

5. **Test Functionality**:
   - Log in as user in `unverified` group → Should see alert
   - Log in as user in `verified` group → Should NOT see alert
   - Click X button → Alert should disappear until next login

## 🔍 Verification Checklist

### ✅ Basic Functionality
- [ ] Page loads without JavaScript errors
- [ ] Only one alert appears (no stacking)
- [ ] Alert shows for `unverified` and `grace_period` groups
- [ ] Alert hidden for `verified` group
- [ ] Close button works and remembers dismissal
- [ ] Alert reappears after logout/login

### ✅ Error Handling
- [ ] Plugin works when groups don't exist (graceful degradation)
- [ ] No console errors when user has no groups
- [ ] Plugin can be disabled via settings
- [ ] Page loads normally when plugin is disabled

### ✅ Edge Cases
- [ ] Works for users with multiple groups
- [ ] Works for users with no groups
- [ ] Works when group names have different cases
- [ ] Works on mobile devices
- [ ] Works with dark theme

## 🚨 Common Issues & Solutions

### Issue: "Page won't load"
**Symptoms**: Blank page or infinite loading
**Solution**: 
1. Check browser console for JavaScript errors
2. Run `window.GetVerifiedAlertSafety.cleanup()` in console
3. Disable plugin temporarily: Set `getverified_alert_enabled` to `false`

### Issue: "Multiple alerts appearing"
**Symptoms**: Stacked alert banners
**Solution**:
1. Run `window.GetVerifiedAlertSafety.cleanup()` in console
2. Refresh the page
3. Check for conflicting plugins

### Issue: "Alert not showing"
**Symptoms**: No alert for unverified users
**Solution**:
1. Verify plugin is enabled: `getverified_alert_enabled = true`
2. Check user is in correct group: `unverified` or `grace_period`
3. Check user is NOT in exclude group: `verified`
4. Clear sessionStorage: `sessionStorage.removeItem('getverified-alert-dismissed')`

### Issue: "Groups don't exist error"
**Symptoms**: Console errors about missing groups
**Solution**:
1. Create required groups in Admin → Groups
2. Or update settings to use existing group names
3. Plugin will work gracefully even with missing groups

## 🔧 Advanced Configuration

### Custom Group Names
If you want to use different group names:

1. Go to Admin → Settings → Plugins
2. Update `getverified_alert_target_groups`: `your_unverified_group|your_grace_group`
3. Update `getverified_alert_exclude_groups`: `your_verified_group`

### Custom Message
1. Go to Admin → Settings → Plugins
2. Update `getverified_alert_message`: `Your custom message here`

### Disable Plugin Temporarily
1. Go to Admin → Settings → Plugins
2. Set `getverified_alert_enabled` to `false`
3. No rebuild required

## 🧪 Testing Commands

### Test Group Membership
```javascript
// Check current user's groups
const user = Discourse.User.current();
console.log('User groups:', user?.groups?.map(g => g.name));

// Check if user should see alert
const groups = user?.groups?.map(g => g.name.toLowerCase()) || [];
const shouldShow = (groups.includes('unverified') || groups.includes('grace_period')) && !groups.includes('verified');
console.log('Should show alert:', shouldShow);
```

### Test Settings
```javascript
// Check plugin settings
const settings = Discourse.SiteSettings;
console.log('Plugin enabled:', settings.getverified_alert_enabled);
console.log('Alert message:', settings.getverified_alert_message);
console.log('Target groups:', settings.getverified_alert_target_groups);
console.log('Exclude groups:', settings.getverified_alert_exclude_groups);
```

## 📞 Support

If issues persist:

1. **Check browser console** for error messages
2. **Check Rails logs** for server-side errors
3. **Use debugging commands** provided above
4. **Disable plugin** if it breaks the site
5. **Report issues** with console logs and steps to reproduce

## 🔄 Recovery Procedures

### Emergency Disable
If plugin breaks the site:

1. **Via Admin Panel**: Set `getverified_alert_enabled` to `false`
2. **Via Rails Console**:
   ```ruby
   SiteSetting.getverified_alert_enabled = false
   ```
3. **Via File System**: Remove plugin directory and rebuild

### Reset Plugin State
```javascript
// Clear all plugin data
sessionStorage.removeItem('getverified-alert-dismissed');
window.GetVerifiedAlertSafety.cleanup();
window.location.reload();
```
