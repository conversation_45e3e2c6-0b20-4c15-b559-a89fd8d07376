import { withPlugin<PERSON><PERSON> } from "discourse/lib/plugin-api";
import { later, cancel } from "@ember/runloop";

export default {
  name: "getverified-alert-safety",
  
  initialize() {
    console.log("[GetVerified Alert Safety] Initializing safety mechanisms");
    
    withPluginApi("0.8.31", (api) => {
      let cleanupTimer = null;
      
      // Safety mechanism to prevent alert stacking
      const preventAlertStacking = () => {
        try {
          const alerts = document.querySelectorAll('.getverified-alert');
          if (alerts.length > 1) {
            console.warn("[GetVerified Alert Safety] Multiple alerts detected, cleaning up");
            // Keep only the first alert, remove the rest
            for (let i = 1; i < alerts.length; i++) {
              alerts[i].remove();
            }
          }
        } catch (error) {
          console.error("[GetVerified Alert Safety] Error in preventAlertStacking:", error);
        }
      };

      // Safety mechanism to clean up orphaned alerts
      const cleanupOrphanedAlerts = () => {
        try {
          const alerts = document.querySelectorAll('.getverified-alert');
          alerts.forEach(alert => {
            // Check if alert is properly connected to a component
            if (!alert.closest('[data-ember-component]') && !alert.hasAttribute('data-alert-id')) {
              console.warn("[GetVerified Alert Safety] Removing orphaned alert");
              alert.remove();
            }
          });
        } catch (error) {
          console.error("[GetVerified Alert Safety] Error in cleanupOrphanedAlerts:", error);
        }
      };

      // Run safety checks on page changes
      api.onPageChange(() => {
        // Cancel previous timer
        if (cleanupTimer) {
          cancel(cleanupTimer);
        }
        
        // Schedule cleanup after a short delay to allow proper rendering
        cleanupTimer = later(() => {
          preventAlertStacking();
          cleanupOrphanedAlerts();
        }, 500);
      });

      // Global error handler for the plugin
      window.addEventListener('error', (event) => {
        if (event.error && event.error.stack && 
            event.error.stack.includes('getverified-alert')) {
          console.error("[GetVerified Alert Safety] Caught plugin error:", event.error);
          
          // Try to clean up any problematic alerts
          try {
            const alerts = document.querySelectorAll('.getverified-alert');
            alerts.forEach(alert => alert.remove());
          } catch (cleanupError) {
            console.error("[GetVerified Alert Safety] Error during cleanup:", cleanupError);
          }
        }
      });

      // Expose cleanup function globally for debugging
      window.GetVerifiedAlertSafety = {
        cleanup: () => {
          try {
            const alerts = document.querySelectorAll('.getverified-alert');
            alerts.forEach(alert => alert.remove());
            sessionStorage.removeItem('getverified-alert-dismissed');
            console.log("[GetVerified Alert Safety] Manual cleanup completed");
          } catch (error) {
            console.error("[GetVerified Alert Safety] Error in manual cleanup:", error);
          }
        },
        
        reset: () => {
          try {
            sessionStorage.removeItem('getverified-alert-dismissed');
            window.location.reload();
          } catch (error) {
            console.error("[GetVerified Alert Safety] Error in reset:", error);
          }
        }
      };
    });
  }
};
