// GetVerified Alert Plugin - Installation Verification Script
// Run this in browser console after plugin installation

(function() {
  console.log("🔍 GetVerified Alert Plugin - Installation Verification");
  console.log("=" .repeat(60));
  
  const results = {
    pluginLoaded: false,
    safetyLoaded: false,
    settingsAvailable: false,
    currentUser: null,
    userGroups: [],
    shouldShowAlert: false,
    alertVisible: false,
    errors: []
  };
  
  try {
    // Check if safety mechanisms are loaded
    if (window.GetVerifiedAlertSafety) {
      results.safetyLoaded = true;
      console.log("✅ Safety mechanisms loaded");
    } else {
      results.errors.push("Safety mechanisms not loaded");
      console.log("❌ Safety mechanisms not loaded");
    }
    
    // Check if settings are available
    if (window.Discourse && window.Discourse.SiteSettings) {
      const settings = window.Discourse.SiteSettings;
      results.settingsAvailable = true;
      
      console.log("✅ Site settings available");
      console.log(`   Plugin enabled: ${settings.getverified_alert_enabled}`);
      console.log(`   Alert message: "${settings.getverified_alert_message}"`);
      console.log(`   Target groups: "${settings.getverified_alert_target_groups}"`);
      console.log(`   Exclude groups: "${settings.getverified_alert_exclude_groups}"`);
      
      if (settings.getverified_alert_enabled) {
        results.pluginLoaded = true;
        console.log("✅ Plugin is enabled");
      } else {
        console.log("⚠️  Plugin is disabled - enable in Admin → Settings → Plugins");
      }
    } else {
      results.errors.push("Site settings not available");
      console.log("❌ Site settings not available");
    }
    
    // Check current user
    if (window.Discourse && window.Discourse.User) {
      const currentUser = window.Discourse.User.current();
      if (currentUser) {
        results.currentUser = currentUser.username;
        results.userGroups = currentUser.groups ? currentUser.groups.map(g => g.name) : [];
        
        console.log("✅ Current user found");
        console.log(`   Username: ${currentUser.username}`);
        console.log(`   Groups: [${results.userGroups.join(', ')}]`);
        
        // Check if user should see alert
        const groupNames = results.userGroups.map(name => name.toLowerCase());
        const targetGroups = ['unverified', 'grace_period'];
        const excludeGroups = ['verified'];
        
        const belongsToTarget = targetGroups.some(group => groupNames.includes(group));
        const belongsToExclude = excludeGroups.some(group => groupNames.includes(group));
        
        results.shouldShowAlert = belongsToTarget && !belongsToExclude;
        
        if (results.shouldShowAlert) {
          console.log("✅ User should see alert (belongs to target groups, not in exclude groups)");
        } else {
          console.log("ℹ️  User should NOT see alert");
          if (!belongsToTarget) {
            console.log("   Reason: Not in target groups (unverified, grace_period)");
          }
          if (belongsToExclude) {
            console.log("   Reason: In exclude groups (verified)");
          }
        }
      } else {
        console.log("ℹ️  No user logged in");
      }
    } else {
      results.errors.push("Discourse User API not available");
      console.log("❌ Discourse User API not available");
    }
    
    // Check if alert is currently visible
    const alertElements = document.querySelectorAll('.getverified-alert');
    results.alertVisible = alertElements.length > 0;
    
    if (alertElements.length > 0) {
      console.log(`✅ Alert is visible (${alertElements.length} element(s) found)`);
      if (alertElements.length > 1) {
        console.log("⚠️  Multiple alerts detected - this may indicate a stacking issue");
      }
    } else {
      console.log("ℹ️  Alert is not visible");
    }
    
    // Check for dismissed state
    const dismissed = sessionStorage.getItem('getverified-alert-dismissed');
    if (dismissed === 'true') {
      console.log("ℹ️  Alert was dismissed this session");
    }
    
  } catch (error) {
    results.errors.push(error.message);
    console.error("❌ Error during verification:", error);
  }
  
  // Summary
  console.log("\n" + "=" .repeat(60));
  console.log("📋 VERIFICATION SUMMARY");
  console.log("=" .repeat(60));
  
  if (results.errors.length === 0) {
    console.log("✅ No critical errors detected");
  } else {
    console.log("❌ Errors detected:");
    results.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log(`Plugin Status: ${results.pluginLoaded ? '✅ Enabled' : '❌ Disabled'}`);
  console.log(`Safety Loaded: ${results.safetyLoaded ? '✅ Yes' : '❌ No'}`);
  console.log(`Current User: ${results.currentUser || 'Not logged in'}`);
  console.log(`User Groups: [${results.userGroups.join(', ')}]`);
  console.log(`Should Show Alert: ${results.shouldShowAlert ? '✅ Yes' : '❌ No'}`);
  console.log(`Alert Visible: ${results.alertVisible ? '✅ Yes' : '❌ No'}`);
  
  // Recommendations
  console.log("\n📝 RECOMMENDATIONS:");
  
  if (!results.pluginLoaded) {
    console.log("1. Enable plugin: Admin → Settings → Plugins → getverified_alert_enabled = true");
  }
  
  if (!results.safetyLoaded) {
    console.log("2. Plugin may not be properly installed - check plugin files");
  }
  
  if (results.currentUser && results.shouldShowAlert && !results.alertVisible) {
    console.log("3. Alert should be visible but isn't - check console for errors");
    console.log("4. Try running: window.GetVerifiedAlertSafety.cleanup()");
  }
  
  if (results.userGroups.length === 0) {
    console.log("5. Create test groups: unverified, grace_period, verified");
    console.log("6. Add current user to 'unverified' group to test alert");
  }
  
  console.log("\n🔧 DEBUGGING COMMANDS:");
  console.log("window.GetVerifiedAlertSafety.cleanup() - Clean up alerts");
  console.log("window.GetVerifiedAlertSafety.reset() - Reset and reload");
  console.log("sessionStorage.removeItem('getverified-alert-dismissed') - Clear dismissal");
  
  return results;
})();
