import { acceptance, exists } from "discourse/tests/helpers/qunit-helpers";
import { test } from "qunit";
import { visit } from "@ember/test-helpers";

acceptance("GetVerified Alert", function (needs) {
  needs.user({
    groups: [
      { id: 1, name: "unverified" }
    ]
  });

  needs.settings({
    getverified_alert_enabled: true,
    getverified_alert_message: "Test verification message",
    getverified_alert_target_groups: "unverified|grace_period",
    getverified_alert_exclude_groups: "verified"
  });

  test("shows alert for unverified users", async function (assert) {
    await visit("/");
    
    assert.ok(
      exists("#getverified-alert"),
      "Alert banner should be visible for unverified users"
    );
    
    assert.ok(
      exists(".getverified-alert-text"),
      "Alert text should be present"
    );
    
    assert.ok(
      exists(".getverified-alert-close"),
      "Close button should be present"
    );
  });
});

acceptance("GetVerified Alert - Verified User", function (needs) {
  needs.user({
    groups: [
      { id: 1, name: "verified" }
    ]
  });

  needs.settings({
    getverified_alert_enabled: true,
    getverified_alert_message: "Test verification message",
    getverified_alert_target_groups: "unverified|grace_period",
    getverified_alert_exclude_groups: "verified"
  });

  test("hides alert for verified users", async function (assert) {
    await visit("/");
    
    assert.notOk(
      exists("#getverified-alert"),
      "Alert banner should not be visible for verified users"
    );
  });
});

acceptance("GetVerified Alert - Disabled", function (needs) {
  needs.user({
    groups: [
      { id: 1, name: "unverified" }
    ]
  });

  needs.settings({
    getverified_alert_enabled: false,
    getverified_alert_message: "Test verification message",
    getverified_alert_target_groups: "unverified|grace_period",
    getverified_alert_exclude_groups: "verified"
  });

  test("hides alert when plugin is disabled", async function (assert) {
    await visit("/");
    
    assert.notOk(
      exists("#getverified-alert"),
      "Alert banner should not be visible when plugin is disabled"
    );
  });
});
