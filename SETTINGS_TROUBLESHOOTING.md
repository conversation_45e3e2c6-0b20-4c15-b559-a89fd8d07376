# 🔧 Settings Not Showing - Troubleshooting Guide

## 🚨 Issue: Plugin Settings But<PERSON> Goes to Admin but Shows No Settings

### **Root Cause Analysis**
The settings button in `/admin/plugins` filters by plugin name prefix, but the settings aren't appearing because:

1. **Settings not properly registered** during plugin initialization
2. **Missing server locales** for setting descriptions  
3. **Incorrect plugin directory name** vs settings prefix mismatch
4. **Plugin not fully loaded** or rebuild required

## 🔍 **Step-by-Step Diagnosis**

### **Step 1: Verify Plugin is Loaded**
1. Go to `/admin/plugins`
2. Look for "discourse-getverified-alert" in the list
3. Check if it shows as "Enabled" or "Disabled"

### **Step 2: Check Settings Manually**
Navigate directly to: `/admin/site_settings/category/all_results?filter=getverified`

**Expected Result**: Should show 4 settings:
- `getverified_alert_enabled`
- `getverified_alert_message` 
- `getverified_alert_target_groups`
- `getverified_alert_exclude_groups`

### **Step 3: Run Debug Script**
Copy and paste the contents of `debug-settings.js` into browser console.

### **Step 4: Check Rails Logs**
Look for these log messages during startup:
```
[GetVerified Alert] Plugin initialized successfully
```

## 🛠️ **Solutions to Try**

### **Solution 1: Force Rebuild Container**
```bash
cd /var/discourse
./launcher rebuild app
```
**Why**: Settings are registered during container build, not runtime.

### **Solution 2: Check Plugin Directory Name**
Ensure plugin is installed in: `/var/discourse/plugins/discourse-getverified-alert/`

**Correct structure**:
```
/var/discourse/plugins/discourse-getverified-alert/
├── plugin.rb
├── config/
│   ├── settings.yml
│   └── locales/
│       └── server.en.yml
└── assets/
```

### **Solution 3: Verify File Contents**
Check that these files exist and have correct content:

**`config/settings.yml`**:
```yaml
plugins:
  getverified_alert_enabled:
    default: false
    client: true
  getverified_alert_message:
    default: "Verify before you start posting as student"
    client: true
  getverified_alert_target_groups:
    default: "unverified|grace_period"
    client: true
  getverified_alert_exclude_groups:
    default: "verified"
    client: true
```

**`config/locales/server.en.yml`**:
```yaml
en:
  site_settings:
    getverified_alert_enabled: "Enable GetVerified alert banner"
    getverified_alert_message: "Alert message text"
    getverified_alert_target_groups: "Groups that should see the alert (pipe-separated)"
    getverified_alert_exclude_groups: "Groups that should NOT see the alert (pipe-separated)"
```

### **Solution 4: Manual Settings Check**
Run in browser console:
```javascript
// Check if settings exist
Object.keys(Discourse.SiteSettings).filter(k => k.includes('getverified'))

// Navigate directly to settings
window.location.href = '/admin/site_settings/category/all_results?filter=getverified'
```

### **Solution 5: Alternative Navigation**
Instead of using the plugin settings button, navigate directly:

1. **Admin → Settings → All Results**
2. **Search for**: `getverified`
3. **Or go to**: `/admin/site_settings/category/plugins`

## 🔍 **Common Issues & Fixes**

### **Issue**: Settings button shows empty page
**Fix**: Navigate to `/admin/site_settings/category/all_results?filter=getverified`

### **Issue**: "No results found" when searching
**Fix**: Plugin not properly loaded - rebuild container

### **Issue**: Settings exist but can't be changed
**Fix**: Check user has admin permissions

### **Issue**: Settings reset after restart
**Fix**: Check file permissions and plugin directory ownership

## 🧪 **Testing Steps**

### **Test 1: Settings Visibility**
1. Navigate to `/admin/site_settings/category/all_results?filter=getverified`
2. Should see 4 settings with descriptions
3. Should be able to toggle `getverified_alert_enabled`

### **Test 2: Settings Persistence**
1. Change `getverified_alert_message` to "Test message"
2. Save changes
3. Refresh page - should retain "Test message"

### **Test 3: Plugin Functionality**
1. Enable `getverified_alert_enabled`
2. Create `unverified` group and add test user
3. Login as test user - should see alert

## 🚨 **Emergency Workarounds**

### **If Settings Still Don't Appear**:

1. **Manual Setting via Rails Console**:
   ```ruby
   # Access Rails console
   ./launcher enter app
   rails c
   
   # Set settings manually
   SiteSetting.getverified_alert_enabled = true
   SiteSetting.getverified_alert_message = "Verify before you start posting as student"
   SiteSetting.getverified_alert_target_groups = "unverified|grace_period"
   SiteSetting.getverified_alert_exclude_groups = "verified"
   ```

2. **Check Plugin Loading**:
   ```ruby
   # In Rails console
   Discourse.plugins.map(&:name)
   # Should include "discourse-getverified-alert"
   ```

## 📋 **Verification Checklist**

- [ ] Plugin appears in `/admin/plugins`
- [ ] Plugin shows as "Enabled"
- [ ] Settings appear in `/admin/site_settings/category/all_results?filter=getverified`
- [ ] Can toggle `getverified_alert_enabled` setting
- [ ] Can modify `getverified_alert_message` setting
- [ ] Settings persist after page refresh
- [ ] No errors in browser console
- [ ] No errors in Rails logs

## 🎯 **Expected Final State**

After successful troubleshooting:

1. **Plugin Settings Button** → Takes you to filtered settings page
2. **Settings Page** → Shows 4 GetVerified Alert settings
3. **Settings Work** → Can enable/disable and modify values
4. **Plugin Functions** → Alert appears when enabled for target groups

## 📞 **If All Else Fails**

1. **Remove and reinstall plugin**:
   ```bash
   rm -rf /var/discourse/plugins/discourse-getverified-alert
   # Re-copy plugin files
   ./launcher rebuild app
   ```

2. **Check Discourse version compatibility**:
   - Plugin requires Discourse 3.0.0+
   - Check `/admin` → About for version

3. **Contact support** with:
   - Rails logs during startup
   - Browser console output from debug script
   - Plugin file structure screenshot
