// Debug script to check plugin settings registration
// Run this in browser console after plugin installation

console.log("🔍 Debugging GetVerified Alert Plugin Settings");
console.log("=" .repeat(50));

// Check if Discourse is available
if (typeof Discourse === 'undefined') {
  console.error("❌ Discourse object not available");
} else {
  console.log("✅ Discourse object available");
}

// Check site settings
if (Discourse.SiteSettings) {
  console.log("✅ SiteSettings available");
  
  // Check for our specific settings
  const settings = Discourse.SiteSettings;
  const ourSettings = [
    'getverified_alert_enabled',
    'getverified_alert_message', 
    'getverified_alert_target_groups',
    'getverified_alert_exclude_groups'
  ];
  
  console.log("\n📋 Checking our plugin settings:");
  ourSettings.forEach(setting => {
    if (setting in settings) {
      console.log(`✅ ${setting}: ${settings[setting]}`);
    } else {
      console.log(`❌ ${setting}: NOT FOUND`);
    }
  });
  
  // Check all settings that contain 'getverified'
  console.log("\n🔍 All settings containing 'getverified':");
  Object.keys(settings).filter(key => key.includes('getverified')).forEach(key => {
    console.log(`   ${key}: ${settings[key]}`);
  });
  
  // Check all plugin settings
  console.log("\n🔍 All settings containing 'plugin' or starting with common prefixes:");
  Object.keys(settings).filter(key => 
    key.includes('plugin') || 
    key.startsWith('discourse_') ||
    key.includes('alert')
  ).forEach(key => {
    console.log(`   ${key}: ${settings[key]}`);
  });
  
} else {
  console.error("❌ SiteSettings not available");
}

// Check if we can access admin settings
console.log("\n🔧 Admin Settings Check:");
if (window.location.pathname.includes('/admin')) {
  console.log("✅ Currently in admin area");
  
  // Try to find settings in the page
  const settingsElements = document.querySelectorAll('[data-setting-name*="getverified"]');
  if (settingsElements.length > 0) {
    console.log(`✅ Found ${settingsElements.length} setting elements in DOM`);
    settingsElements.forEach(el => {
      console.log(`   Setting: ${el.getAttribute('data-setting-name')}`);
    });
  } else {
    console.log("❌ No setting elements found in DOM");
  }
} else {
  console.log("ℹ️  Not in admin area - navigate to /admin/site_settings to see settings");
}

// Check current URL and suggest navigation
console.log("\n🧭 Navigation Help:");
console.log(`Current URL: ${window.location.href}`);
console.log("To see plugin settings, navigate to:");
console.log("   /admin/site_settings/category/plugins");
console.log("   /admin/site_settings/category/all_results?filter=getverified");

// Check if plugin is loaded
console.log("\n🔌 Plugin Loading Check:");
if (window.GetVerifiedAlertDebug) {
  console.log("✅ Plugin debug tools loaded");
  try {
    window.GetVerifiedAlertDebug.checkSettings();
  } catch (e) {
    console.error("❌ Error calling debug tools:", e);
  }
} else {
  console.log("❌ Plugin debug tools not loaded");
}

// Final recommendations
console.log("\n💡 RECOMMENDATIONS:");
console.log("1. If settings are not showing, try rebuilding Discourse container");
console.log("2. Check Rails logs for plugin loading errors");
console.log("3. Verify plugin files are in correct directory structure");
console.log("4. Navigate directly to: /admin/site_settings/category/all_results?filter=getverified");

console.log("\n🔧 MANUAL COMMANDS TO TRY:");
console.log("// Check if settings exist in SiteSettings object");
console.log("Object.keys(Discourse.SiteSettings).filter(k => k.includes('getverified'))");
console.log("");
console.log("// Navigate to plugin settings");
console.log("window.location.href = '/admin/site_settings/category/plugins'");
console.log("");
console.log("// Search for our settings");
console.log("window.location.href = '/admin/site_settings/category/all_results?filter=getverified'");
