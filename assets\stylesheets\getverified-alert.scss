/* GetVerified Alert Plugin Styles */

.getverified-alert {
  background-color: #e6f0ff;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
  margin: 0;
  padding: 0;
  position: relative;
  width: 100%;
  z-index: 1000;
  
  /* Ensure alert appears above other content */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .getverified-alert-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    max-width: 1200px;
    margin: 0 auto;
    
    .getverified-alert-text {
      color: #1a365d;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.4;
      margin: 0;
      flex: 1;
      
      /* Ensure text is readable */
      text-shadow: none;
    }
    
    .getverified-alert-close {
      background: none;
      border: none;
      color: #1a365d;
      cursor: pointer;
      padding: 4px;
      margin-left: 12px;
      border-radius: 3px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: rgba(26, 54, 93, 0.1);
      }
      
      &:focus {
        outline: 2px solid #4299e1;
        outline-offset: 2px;
      }
      
      svg {
        width: 14px;
        height: 14px;
        fill: currentColor;
      }
    }
  }
}

/* Responsive design for mobile devices */
@media (max-width: 768px) {
  .getverified-alert {
    .getverified-alert-content {
      padding: 10px 12px;
      
      .getverified-alert-text {
        font-size: 13px;
        padding-right: 8px;
      }
      
      .getverified-alert-close {
        margin-left: 8px;
        padding: 6px;
        
        svg {
          width: 12px;
          height: 12px;
        }
      }
    }
  }
}

/* Dark theme support */
.dark-scheme .getverified-alert {
  background-color: #1a365d;
  border-color: #2d5a87;
  
  .getverified-alert-content {
    .getverified-alert-text {
      color: #e6f0ff;
    }
    
    .getverified-alert-close {
      color: #e6f0ff;
      
      &:hover {
        background-color: rgba(230, 240, 255, 0.1);
      }
      
      &:focus {
        outline-color: #63b3ed;
      }
    }
  }
}

/* Ensure alert doesn't interfere with site header */
body.has-getverified-alert {
  .d-header-wrap {
    margin-top: 0;
  }
}

/* Animation for smooth appearance */
.getverified-alert {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
