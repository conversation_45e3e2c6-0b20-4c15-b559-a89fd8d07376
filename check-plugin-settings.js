// Quick check for plugin settings button functionality
// Run this in browser console on /admin/plugins page

console.log("🔍 Checking Plugin Settings Button Functionality");
console.log("=" .repeat(50));

// Check current page
if (!window.location.pathname.includes('/admin/plugins')) {
  console.log("⚠️  Navigate to /admin/plugins first");
  console.log("Current URL:", window.location.href);
} else {
  console.log("✅ On /admin/plugins page");
}

// Look for our plugin in the DOM
const pluginElements = document.querySelectorAll('[data-plugin-name]');
console.log(`\n📋 Found ${pluginElements.length} plugins in DOM:`);

pluginElements.forEach(el => {
  const pluginName = el.getAttribute('data-plugin-name');
  console.log(`   Plugin: ${pluginName}`);
  
  if (pluginName && pluginName.includes('getverified')) {
    console.log(`   ✅ Found our plugin: ${pluginName}`);
    
    // Look for settings button
    const settingsButton = el.querySelector('button[title*="Settings"], a[href*="settings"], .btn[href*="settings"]');
    if (settingsButton) {
      console.log(`   ✅ Settings button found`);
      console.log(`   Button text: "${settingsButton.textContent.trim()}"`);
      console.log(`   Button href/onclick:`, settingsButton.href || settingsButton.onclick);
    } else {
      console.log(`   ❌ No settings button found`);
    }
  }
});

// Check if we can find our plugin by name variations
const possibleNames = [
  'discourse-getverified-alert',
  'getverified-alert', 
  'getverified_alert',
  'getverified'
];

console.log(`\n🔍 Searching for plugin by name variations:`);
possibleNames.forEach(name => {
  const found = document.querySelector(`[data-plugin-name="${name}"]`);
  if (found) {
    console.log(`   ✅ Found plugin with name: ${name}`);
  } else {
    console.log(`   ❌ Not found: ${name}`);
  }
});

// Check site settings for our prefix
console.log(`\n📋 Checking SiteSettings for our prefix:`);
if (Discourse && Discourse.SiteSettings) {
  const ourSettings = Object.keys(Discourse.SiteSettings).filter(key => 
    key.startsWith('getverified_alert_')
  );
  
  console.log(`Found ${ourSettings.length} settings with our prefix:`);
  ourSettings.forEach(setting => {
    console.log(`   ${setting}: ${Discourse.SiteSettings[setting]}`);
  });
  
  if (ourSettings.length === 0) {
    console.log("❌ No settings found with getverified_alert_ prefix");
    console.log("This means the plugin settings are not loaded");
  }
} else {
  console.log("❌ Discourse.SiteSettings not available");
}

// Check what happens when we try to navigate to settings
console.log(`\n🔧 Manual Navigation Test:`);
console.log("Try these URLs manually:");
console.log("1. /admin/site_settings/category/plugins?filter=getverified_alert");
console.log("2. /admin/site_settings/category/all_results?filter=getverified_alert");

// Provide manual commands
console.log(`\n💡 Manual Commands to Try:`);
console.log("// Navigate to plugin settings");
console.log("window.location.href = '/admin/site_settings/category/plugins?filter=getverified_alert'");
console.log("");
console.log("// Check if settings exist");
console.log("Object.keys(Discourse.SiteSettings).filter(k => k.includes('getverified'))");
