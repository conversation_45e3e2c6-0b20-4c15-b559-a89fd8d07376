import { apiInitializer } from "discourse/lib/api";

export default apiInitializer("1.14.0", (api) => {
  console.log("[GetVerified Alert] Initializer loaded");

  // Global cleanup function for debugging
  window.GetVerifiedAlertDebug = {
    cleanup: () => {
      try {
        const alerts = document.querySelectorAll('.getverified-alert');
        alerts.forEach(alert => alert.remove());
        sessionStorage.removeItem('getverified-alert-dismissed');
        console.log("[GetVerified Alert Debug] Manual cleanup completed");
      } catch (error) {
        console.error("[GetVerified Alert Debug] Error in manual cleanup:", error);
      }
    },

    reset: () => {
      try {
        sessionStorage.removeItem('getverified-alert-dismissed');
        window.location.reload();
      } catch (error) {
        console.error("[GetVerified Alert Debug] Error in reset:", error);
      }
    },

    checkSettings: () => {
      try {
        const settings = api.container.lookup('service:site-settings');
        console.log("[GetVerified Alert Debug] Settings:", {
          enabled: settings.discourse_getverified_alert_enabled,
          message: settings.discourse_getverified_alert_message,
          targetGroups: settings.discourse_getverified_alert_target_groups,
          excludeGroups: settings.discourse_getverified_alert_exclude_groups
        });
      } catch (error) {
        console.error("[GetVerified Alert Debug] Error checking settings:", error);
      }
    }
  };
});
