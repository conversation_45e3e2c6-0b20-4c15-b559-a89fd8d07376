# Discourse GetVerified Alert Plugin

A Discourse plugin that displays an information alert banner above the navbar for users who need to verify their accounts.

## Features

- **Targeted Display**: Shows alert only to users in specific groups (default: `unverified`, `grace_period`)
- **Smart Exclusion**: Hides alert from users in verified groups (default: `verified`)
- **Dismissible**: Users can close the alert, and it stays hidden until next login
- **Customizable**: Configurable message text and target groups via admin settings
- **Responsive**: Works on both desktop and mobile devices
- **Theme Support**: Automatically adapts to light and dark themes
- **Modern Design**: Clean blue banner with smooth animations

## Installation

1. **Clone or download** this plugin to your Discourse `plugins` directory:
   ```bash
   cd /var/discourse/plugins
   git clone https://github.com/yourusername/discourse-getverified-alert.git
   ```

2. **Rebuild your Discourse container**:
   ```bash
   cd /var/discourse
   ./launcher rebuild app
   ```

3. **Enable the plugin** in your Discourse admin panel:
   - Go to Admin → Plugins
   - Find "discourse-getverified-alert" and enable it

## Configuration

The plugin can be configured through the admin settings:

### Site Settings

Navigate to **Admin → Settings → Plugins** to configure:

- **`getverified_alert_enabled`** (boolean, default: `true`)
  - Enable or disable the alert banner globally

- **`getverified_alert_message`** (string, default: `"Verify before you start posting as student"`)
  - The message text displayed in the alert banner

- **`getverified_alert_target_groups`** (group list, default: `"unverified|grace_period"`)
  - Groups that should see the alert (pipe-separated list)

- **`getverified_alert_exclude_groups`** (group list, default: `"verified"`)
  - Groups that should NOT see the alert (pipe-separated list)

### Group Setup

Make sure you have the following groups created in your Discourse instance:

1. **`unverified`** - Users who haven't completed verification
2. **`grace_period`** - Users in a grace period before verification required
3. **`verified`** - Users who have completed verification

You can customize these group names in the plugin settings.

## How It Works

1. **User Login**: When a user logs in, the plugin checks their group membership
2. **Group Check**: If the user belongs to target groups AND doesn't belong to exclude groups, the alert is shown
3. **Display**: Alert appears above the site header with the configured message
4. **Dismissal**: User can click the X button to dismiss the alert
5. **Session Storage**: Dismissal is remembered until the user logs out/in again

## Styling

The plugin includes responsive CSS that:
- Uses a blue color scheme (`#e6f0ff` background, `#b3d9ff` border)
- Adapts to dark themes automatically
- Provides smooth slide-down animation
- Works on mobile devices

### Custom Styling

You can override the default styles by adding CSS to your theme:

```scss
.getverified-alert {
  background-color: your-color;
  border-color: your-border-color;
  
  .getverified-alert-text {
    color: your-text-color;
  }
}
```

## Compatibility

- **Discourse Version**: 3.0.0 and above
- **Browser Support**: All modern browsers
- **Mobile**: Fully responsive design

## Development

### File Structure

```
discourse-getverified-alert/
├── plugin.rb                                    # Plugin manifest
├── config/
│   └── settings.yml                             # Site settings configuration
├── assets/
│   ├── javascripts/
│   │   └── discourse/
│   │       ├── initializers/
│   │       │   └── getverified-alert.js         # Legacy initializer (backup)
│   │       ├── connectors/
│   │       │   └── above-site-header/
│   │       │       └── getverified-alert.js     # Template connector logic
│   │       └── templates/
│   │           └── connectors/
│   │               └── above-site-header/
│   │                   └── getverified-alert.hbs # Handlebars template
│   └── stylesheets/
│       └── getverified-alert.scss               # Plugin styles
└── README.md                                    # This file
```

### Testing

1. Create test groups: `unverified`, `grace_period`, `verified`
2. Add test users to different groups
3. Log in as different users to verify alert behavior
4. Test dismissal functionality
5. Verify responsive design on mobile

## Troubleshooting

### Alert Not Showing

1. Check if plugin is enabled in Admin → Plugins
2. Verify user belongs to target groups
3. Ensure user doesn't belong to exclude groups
4. Check if alert was dismissed (try logging out/in)
5. Verify `getverified_alert_enabled` setting is true

### Styling Issues

1. Clear browser cache
2. Check for CSS conflicts with theme
3. Verify plugin assets are loaded
4. Check browser console for JavaScript errors

## License

This plugin is released under the MIT License.

## Support

For issues and feature requests, please use the GitHub issue tracker.
