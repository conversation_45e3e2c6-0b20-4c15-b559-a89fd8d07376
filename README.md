# Discourse GetVerified Alert Plugin

A robust Discourse plugin that displays an information alert banner above the navbar for users who need to verify their accounts.

## 🚨 Version 1.1.0 - Critical Fixes Applied

This version addresses critical issues identified in production:
- ✅ **Fixed page loading issues** - Added comprehensive error handling
- ✅ **Prevented alert stacking** - Implemented cleanup mechanisms
- ✅ **Added graceful group handling** - Plugin works even when groups don't exist
- ✅ **Enhanced debugging** - Added console logging and safety mechanisms
- ✅ **Improved stability** - Plugin disabled by default for safe installation

## Features

- **Targeted Display**: Shows alert only to users in specific groups (default: `unverified`, `grace_period`)
- **Smart Exclusion**: Hides alert from users in verified groups (default: `verified`)
- **Dismissible**: Users can close the alert, and it stays hidden until next login
- **Customizable**: Configurable message text and target groups via admin settings
- **Responsive**: Works on both desktop and mobile devices
- **Theme Support**: Automatically adapts to light and dark themes
- **Modern Design**: Clean blue banner with smooth animations

## Installation

1. **Clone or download** this plugin to your Discourse `plugins` directory:
   ```bash
   cd /var/discourse/plugins
   git clone https://github.com/yourusername/discourse-getverified-alert.git
   ```

2. **Rebuild your Discourse container**:
   ```bash
   cd /var/discourse
   ./launcher rebuild app
   ```

3. **Enable the plugin** in your Discourse admin panel:
   - Go to Admin → Plugins → discourse-getverified-alert → Settings
   - Find `discourse_getverified_alert_enabled` and set to `true`
   - Plugin is disabled by default for safety

## Configuration

The plugin can be configured through the admin settings:

### Site Settings

Navigate to **Admin → Settings → Plugins** to configure:

- **`discourse_getverified_alert_enabled`** (boolean, default: `false`)
  - Enable or disable the alert banner globally

- **`discourse_getverified_alert_message`** (string, default: `"Verify before you start posting as student"`)
  - The message text displayed in the alert banner

- **`discourse_getverified_alert_target_groups`** (string, default: `"unverified|grace_period"`)
  - Groups that should see the alert (pipe-separated list)

- **`discourse_getverified_alert_exclude_groups`** (string, default: `"verified"`)
  - Groups that should NOT see the alert (pipe-separated list)

### Group Setup

Make sure you have the following groups created in your Discourse instance:

1. **`unverified`** - Users who haven't completed verification
2. **`grace_period`** - Users in a grace period before verification required
3. **`verified`** - Users who have completed verification

You can customize these group names in the plugin settings.

## How It Works

1. **User Login**: When a user logs in, the plugin checks their group membership
2. **Group Check**: If the user belongs to target groups AND doesn't belong to exclude groups, the alert is shown
3. **Display**: Alert appears above the site header with the configured message
4. **Dismissal**: User can click the X button to dismiss the alert
5. **Session Storage**: Dismissal is remembered until the user logs out/in again

## Styling

The plugin includes responsive CSS that:
- Uses a blue color scheme (`#e6f0ff` background, `#b3d9ff` border)
- Adapts to dark themes automatically
- Provides smooth slide-down animation
- Works on mobile devices

### Custom Styling

You can override the default styles by adding CSS to your theme:

```scss
.getverified-alert {
  background-color: your-color;
  border-color: your-border-color;

  .getverified-alert-text {
    color: your-text-color;
  }
}
```

## Compatibility

- **Discourse Version**: 3.0.0 and above
- **Browser Support**: All modern browsers
- **Mobile**: Fully responsive design

## Development

### File Structure

```
discourse-getverified-alert/
├── plugin.rb                                    # Plugin manifest
├── config/
│   └── settings.yml                             # Site settings configuration
├── assets/
│   ├── javascripts/
│   │   └── discourse/
│   │       ├── initializers/
│   │       │   └── getverified-alert.js         # Legacy initializer (backup)
│   │       ├── connectors/
│   │       │   └── above-site-header/
│   │       │       └── getverified-alert.js     # Template connector logic
│   │       └── templates/
│   │           └── connectors/
│   │               └── above-site-header/
│   │                   └── getverified-alert.hbs # Handlebars template
│   └── stylesheets/
│       └── getverified-alert.scss               # Plugin styles
└── README.md                                    # This file
```

### Testing

1. Create test groups: `unverified`, `grace_period`, `verified`
2. Add test users to different groups
3. Log in as different users to verify alert behavior
4. Test dismissal functionality
5. Verify responsive design on mobile

## 🛡️ Safety Features

### Error Handling
- **Graceful degradation** when groups don't exist
- **Comprehensive error catching** prevents page crashes
- **Safe defaults** with plugin disabled by default
- **Cleanup mechanisms** prevent alert stacking
- **Debug logging** for easy troubleshooting

### Emergency Controls
```javascript
// Emergency cleanup (run in browser console)
window.GetVerifiedAlertDebug.cleanup();

// Reset plugin state
window.GetVerifiedAlertDebug.reset();

// Check plugin settings
window.GetVerifiedAlertDebug.checkSettings();
```

## Troubleshooting

### Settings Not Appearing

**Issue**: Plugin settings button goes to admin but shows no settings
**Solutions**:
1. Navigate directly to: `/admin/site_settings/category/all_results?filter=getverified`
2. Rebuild container: `./launcher rebuild app`
3. Run debug script: Copy `debug-settings.js` to browser console
4. See detailed guide: `SETTINGS_TROUBLESHOOTING.md`

### Alert Not Showing

1. Check if plugin is enabled: `discourse_getverified_alert_enabled = true`
2. Verify user belongs to target groups (`unverified`, `grace_period`)
3. Ensure user doesn't belong to exclude groups (`verified`)
4. Check if alert was dismissed: Clear sessionStorage
5. Check browser console for error messages

### Page Loading Issues

1. Open browser console and check for JavaScript errors
2. Run emergency cleanup: `window.GetVerifiedAlertDebug.cleanup()`
3. Temporarily disable plugin: Set `getverified_alert_enabled = false`
4. See `TROUBLESHOOTING.md` for detailed solutions

### Multiple Alerts Appearing

1. Run cleanup command: `window.GetVerifiedAlertDebug.cleanup()`
2. Refresh the page
3. Check for conflicting plugins or themes

## License

This plugin is released under the MIT License.

## Support

For issues and feature requests, please use the GitHub issue tracker.
