import { computed } from "@ember/object";
import { scheduleOnce } from "@ember/runloop";

export default {
  setupComponent(args, component) {
    console.log("[GetVerified Alert] Setting up component", args);

    // Prevent multiple setups
    if (component._getverifiedAlertSetup) {
      console.log("[GetVerified Alert] Component already setup, skipping");
      return;
    }
    component._getverifiedAlertSetup = true;

    // Clean up any existing alerts first
    this.cleanupExistingAlerts();

    try {
      // Set up computed property to determine if alert should be shown
      component.set('shouldShowAlert', computed('currentUser.groups.[]', function() {
        try {
          console.log("[GetVerified Alert] Computing shouldShowAlert");

          // Check if plugin is enabled via site settings
          const siteSettings = this.siteSettings || {};
          if (!siteSettings.getverified_alert_enabled) {
            console.log("[GetVerified Alert] Plugin disabled in settings");
            return false;
          }

          const currentUser = this.currentUser;

          // Don't show alert if user is not logged in
          if (!currentUser) {
            console.log("[GetVerified Alert] No current user");
            return false;
          }

          // Check if alert was dismissed for this session
          const alertDismissed = sessionStorage.getItem('getverified-alert-dismissed');
          if (alertDismissed === 'true') {
            console.log("[GetVerified Alert] Alert was dismissed this session");
            return false;
          }

          // Safely get user groups with null checks
          const userGroups = currentUser.groups || [];
          if (!Array.isArray(userGroups)) {
            console.log("[GetVerified Alert] User groups is not an array:", userGroups);
            return false;
          }

          const groupNames = userGroups
            .filter(group => group && group.name) // Filter out null/undefined groups
            .map(group => group.name.toLowerCase());

          console.log("[GetVerified Alert] User groups:", groupNames);

          // Get target groups from settings with safe defaults
          let targetGroups = [];
          try {
            const targetGroupsSetting = siteSettings.getverified_alert_target_groups || "unverified|grace_period";
            targetGroups = targetGroupsSetting
              .split('|')
              .map(group => group.trim().toLowerCase())
              .filter(group => group.length > 0); // Remove empty strings
          } catch (e) {
            console.warn("[GetVerified Alert] Error parsing target groups:", e);
            targetGroups = ["unverified", "grace_period"]; // Safe fallback
          }

          // Get exclude groups from settings with safe defaults
          let excludeGroups = [];
          try {
            const excludeGroupsSetting = siteSettings.getverified_alert_exclude_groups || "verified";
            excludeGroups = excludeGroupsSetting
              .split('|')
              .map(group => group.trim().toLowerCase())
              .filter(group => group.length > 0); // Remove empty strings
          } catch (e) {
            console.warn("[GetVerified Alert] Error parsing exclude groups:", e);
            excludeGroups = ["verified"]; // Safe fallback
          }

          console.log("[GetVerified Alert] Target groups:", targetGroups);
          console.log("[GetVerified Alert] Exclude groups:", excludeGroups);

          // Check if user belongs to any target groups
          const belongsToTargetGroup = targetGroups.some(group => groupNames.includes(group));

          // Check if user belongs to any exclude groups
          const belongsToExcludeGroup = excludeGroups.some(group => groupNames.includes(group));

          const shouldShow = belongsToTargetGroup && !belongsToExcludeGroup;
          console.log("[GetVerified Alert] Should show alert:", shouldShow);

          return shouldShow;

        } catch (error) {
          console.error("[GetVerified Alert] Error in shouldShowAlert computation:", error);
          return false; // Fail safely
        }
      }));

      // Set up computed property for alert message
      component.set('alertMessage', computed(function() {
        try {
          const siteSettings = this.siteSettings || {};
          const customMessage = siteSettings.getverified_alert_message;

          if (customMessage) {
            return customMessage;
          }

          // Fallback to localized string or hardcoded default
          if (typeof I18n !== 'undefined' && I18n.t) {
            return I18n.t("js.getverified_alert.default_message", {
              defaultValue: "Verify before you start posting as student"
            });
          }

          return "Verify before you start posting as student";
        } catch (error) {
          console.error("[GetVerified Alert] Error getting alert message:", error);
          return "Verify before you start posting as student";
        }
      }));

      // Set up action to dismiss the alert
      component.set('actions', {
        dismissAlert() {
          try {
            console.log("[GetVerified Alert] Dismissing alert");
            sessionStorage.setItem('getverified-alert-dismissed', 'true');
            component.set('shouldShowAlert', false);

            // Also clean up any DOM elements as backup
            scheduleOnce('afterRender', this, this.cleanupExistingAlerts);
          } catch (error) {
            console.error("[GetVerified Alert] Error dismissing alert:", error);
          }
        }
      });

    } catch (error) {
      console.error("[GetVerified Alert] Error in setupComponent:", error);
    }
  },

  cleanupExistingAlerts() {
    try {
      // Remove any existing alert elements to prevent stacking
      const existingAlerts = document.querySelectorAll('#getverified-alert, .getverified-alert');
      existingAlerts.forEach(alert => {
        console.log("[GetVerified Alert] Removing existing alert element");
        alert.remove();
      });
    } catch (error) {
      console.error("[GetVerified Alert] Error cleaning up existing alerts:", error);
    }
  },

  teardownComponent() {
    try {
      console.log("[GetVerified Alert] Tearing down component");
      this.cleanupExistingAlerts();
    } catch (error) {
      console.error("[GetVerified Alert] Error in teardown:", error);
    }
  }
};
