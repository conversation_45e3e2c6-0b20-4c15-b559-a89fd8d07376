import { computed } from "@ember/object";

export default {
  setupComponent(args, component) {
    // Set up computed property to determine if alert should be shown
    component.set('shouldShowAlert', computed('currentUser.groups.[]', function() {
      // Check if plugin is enabled
      if (!this.siteSettings.getverified_alert_enabled) {
        return false;
      }

      const currentUser = this.currentUser;

      // Don't show alert if user is not logged in
      if (!currentUser) {
        return false;
      }

      // Check if alert was dismissed for this session
      const alertDismissed = sessionStorage.getItem('getverified-alert-dismissed');
      if (alertDismissed === 'true') {
        return false;
      }

      // Get user groups
      const userGroups = currentUser.groups || [];
      const groupNames = userGroups.map(group => group.name.toLowerCase());

      // Get target groups from settings (default: unverified, grace_period)
      const targetGroups = this.siteSettings.getverified_alert_target_groups
        .split('|')
        .map(group => group.trim().toLowerCase());

      // Get exclude groups from settings (default: verified)
      const excludeGroups = this.siteSettings.getverified_alert_exclude_groups
        .split('|')
        .map(group => group.trim().toLowerCase());

      // Check if user belongs to any target groups
      const belongsToTargetGroup = targetGroups.some(group => groupNames.includes(group));

      // Check if user belongs to any exclude groups
      const belongsToExcludeGroup = excludeGroups.some(group => groupNames.includes(group));

      return belongsToTargetGroup && !belongsToExcludeGroup;
    }));

    // Set up computed property for alert message
    component.set('alertMessage', computed(function() {
      return this.siteSettings.getverified_alert_message ||
             I18n.t("js.getverified_alert.default_message");
    }));

    // Set up action to dismiss the alert
    component.set('actions', {
      dismissAlert() {
        sessionStorage.setItem('getverified-alert-dismissed', 'true');
        component.set('shouldShowAlert', false);
      }
    });
  }
};
