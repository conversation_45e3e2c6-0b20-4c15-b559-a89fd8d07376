import Component from "@glimmer/component";
import { tracked } from "@glimmer/tracking";
import { action } from "@ember/object";
import { service } from "@ember/service";

export default class GetVerifiedAlert extends Component {
  @service siteSettings;
  @service currentUser;
  @tracked isDismissed = false;

  constructor() {
    super(...arguments);
    console.log("[GetVerified Alert] Component initialized");

    // Check if alert was dismissed this session
    this.isDismissed = sessionStorage.getItem('getverified-alert-dismissed') === 'true';

    // Clean up any existing alerts to prevent stacking
    this.cleanupExistingAlerts();
  }

  get shouldShowAlert() {
    try {
      console.log("[GetVerified Alert] Computing shouldShowAlert");

      // Don't show if dismissed
      if (this.isDismissed) {
        console.log("[GetVerified Alert] Alert was dismissed this session");
        return false;
      }

      // Check if plugin is enabled
      if (!this.siteSettings.discourse_getverified_alert_enabled) {
        console.log("[GetVerified Alert] Plugin disabled in settings");
        return false;
      }

      // Don't show alert if user is not logged in
      if (!this.currentUser) {
        console.log("[GetVerified Alert] No current user");
        return false;
      }

      // Safely get user groups with null checks
      const userGroups = this.currentUser.groups || [];
      if (!Array.isArray(userGroups)) {
        console.log("[GetVerified Alert] User groups is not an array:", userGroups);
        return false;
      }

      const groupNames = userGroups
        .filter(group => group && group.name) // Filter out null/undefined groups
        .map(group => group.name.toLowerCase());

      console.log("[GetVerified Alert] User groups:", groupNames);

      // Get target groups from settings with safe defaults
      let targetGroups = [];
      try {
        const targetGroupsSetting = this.siteSettings.discourse_getverified_alert_target_groups || "unverified|grace_period";
        targetGroups = targetGroupsSetting
          .split('|')
          .map(group => group.trim().toLowerCase())
          .filter(group => group.length > 0); // Remove empty strings
      } catch (e) {
        console.warn("[GetVerified Alert] Error parsing target groups:", e);
        targetGroups = ["unverified", "grace_period"]; // Safe fallback
      }

      // Get exclude groups from settings with safe defaults
      let excludeGroups = [];
      try {
        const excludeGroupsSetting = this.siteSettings.discourse_getverified_alert_exclude_groups || "verified";
        excludeGroups = excludeGroupsSetting
          .split('|')
          .map(group => group.trim().toLowerCase())
          .filter(group => group.length > 0); // Remove empty strings
      } catch (e) {
        console.warn("[GetVerified Alert] Error parsing exclude groups:", e);
        excludeGroups = ["verified"]; // Safe fallback
      }

      console.log("[GetVerified Alert] Target groups:", targetGroups);
      console.log("[GetVerified Alert] Exclude groups:", excludeGroups);

      // Check if user belongs to any target groups
      const belongsToTargetGroup = targetGroups.some(group => groupNames.includes(group));

      // Check if user belongs to any exclude groups
      const belongsToExcludeGroup = excludeGroups.some(group => groupNames.includes(group));

      const shouldShow = belongsToTargetGroup && !belongsToExcludeGroup;
      console.log("[GetVerified Alert] Should show alert:", shouldShow);

      return shouldShow;

    } catch (error) {
      console.error("[GetVerified Alert] Error in shouldShowAlert computation:", error);
      return false; // Fail safely
    }
  }

  get alertMessage() {
    try {
      const customMessage = this.siteSettings.discourse_getverified_alert_message;

      if (customMessage) {
        return customMessage;
      }

      // Fallback to localized string or hardcoded default
      if (typeof I18n !== 'undefined' && I18n.t) {
        return I18n.t("js.getverified_alert.default_message", {
          defaultValue: "Verify before you start posting as student"
        });
      }

      return "Verify before you start posting as student";
    } catch (error) {
      console.error("[GetVerified Alert] Error getting alert message:", error);
      return "Verify before you start posting as student";
    }
  }

  @action
  dismissAlert() {
    try {
      console.log("[GetVerified Alert] Dismissing alert");
      sessionStorage.setItem('getverified-alert-dismissed', 'true');
      this.isDismissed = true;

      // Also clean up any DOM elements as backup
      this.cleanupExistingAlerts();
    } catch (error) {
      console.error("[GetVerified Alert] Error dismissing alert:", error);
    }
  }

  cleanupExistingAlerts() {
    try {
      // Remove any existing alert elements to prevent stacking
      const existingAlerts = document.querySelectorAll('.getverified-alert:not([data-component-id])');
      existingAlerts.forEach(alert => {
        console.log("[GetVerified Alert] Removing existing alert element");
        alert.remove();
      });
    } catch (error) {
      console.error("[GetVerified Alert] Error cleaning up existing alerts:", error);
    }
  }

  willDestroy() {
    try {
      console.log("[GetVerified Alert] Component destroying");
      super.willDestroy();
    } catch (error) {
      console.error("[GetVerified Alert] Error in willDestroy:", error);
    }
  }
}
